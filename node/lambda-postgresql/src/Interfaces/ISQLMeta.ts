import { IColumnInfo } from "./ISQLBase";

/**
 * Query result
 */
export interface IQueryResult<T = any> {
  data: T[];
  fields?: IColumnInfo[];
  affectedRows?: number;
  insertId?: number;
}

/**
 * Table information
 */
export interface ITableInfo {
  table_name: string;
  table_comment?: string;
  table_schema: string;
  table_type?: string; // Add this if not present
}
/**
 * Query options
 */
export interface IQueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: "ASC" | "DESC";
}
