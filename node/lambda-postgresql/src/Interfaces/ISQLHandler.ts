import { IUsage } from "/opt/nodejs/Store/UsageBase";

/**
 * SQL Meta Methods (similar to Salesforce EMetaRoutes)
 */
export enum ESQLMetaRoutes {
  "getDatabases" = "getDatabases",
  "getTables" = "getTables",
  "getFieldsForTable" = "getFieldsForTable",
  "getPrimaryKeys" = "getPrimaryKeys",
}

/**
 * SQL Data Methods (similar to Salesforce)
 */
export enum ESQLDataRoutes {
  "select" = "select",
  "create" = "create",
  "update" = "update",
  "upsert" = "upsert",
  "delete" = "delete",
  "query" = "query",
  "mcpSelect" = "select",
}

// Array of meta methods for easy checking
export const META_METHODS = Object.values(ESQLMetaRoutes);

/**
 * Condition operators (similar to Salesforce)
 */
export type TConditionOption =
  | "equals"
  | "notEqual"
  | "lessThan"
  | "greaterThan"
  | "lessOrEqual"
  | "greaterOrEqual"
  | "contains"
  | "notContain"
  | "startsWith"
  | "endsWith"
  | "isEmpty"
  | "isNotEmpty"
  | "in";

/**
 * Post condition connectors
 */
export type TPostConditionOption = "and" | "or" | "";

/**
 * Sort options
 */
export type TSortOption = "ascend" | "descend";

/**
 * Condition structure (similar to Salesforce)
 */
export type TCondition = {
  Field: string;
  Value: string | string[];
  Type?: string;
  Condition: TConditionOption;
  Post: TPostConditionOption;
};

/**
 * Sort structure
 */
export type TSort = {
  Field: string;
  Sort: TSortOption;
};

/**
 * MCP Query structure for advanced querying
 */
export type TMcpQuery = {
  field: string;
  operator: string;
  value: string | number | boolean;
  type: string;
  post?: string;
};

/**
 * Field mapping for create/update operations
 */
export type TFieldMapping = {
  _id: string;
  Field: {
    _id: string;
    Type: string;
    Dynamic: {
      value: string;
      label: string;
      type: string;
      required: boolean;
    };
    Value: string;
  };
  Value: {
    _id: string;
    Type: string;
    Dynamic: {};
    Value: string;
  };
};

/**
 * Enhanced SQL payload structure (similar to Salesforce)
 */
export interface ISQLPayload {
  // Basic info
  database: string;
  table: string;
  action: string; // select, create, update, upsert, delete

  // Query conditions
  conditions?: TCondition[];
  sort?: TSort[];
  fetch?: "first" | "all" | "limits";
  limit?: number;

  // MCP Query support
  mcpQuery?: TMcpQuery[];
  mcpLimit?: number;
  mcpAction?: "count" | "select";

  // Data for create/update/upsert
  pushMappingMeta?: TFieldMapping[];
  pushMapping?: Record<string, any>;
  uniqueKey?: Record<string, any>; // for upsert operations

  // Legacy support
  method?: string;
  query?: string;
  params?: any[];
  data?: Record<string, any>;
  where?: Record<string, any>;
  fields?: string[];
  offset?: number;
  orderBy?: string;
  orderDirection?: "ASC" | "DESC";
}

/**
 * Response format (similar to Salesforce)
 */
export interface IAPIResponse<T = any> {
  status: boolean;
  data?: T;
  payload?: T;
  error?: string;
  errorCode?: string;
  affectedRows?: number;
  insertId?: number;
}

/**
 * Response interface
 */
export type IResponseInterface = {
  status?: boolean;
  error?: any;
  errorCode?: any;
  data?: any;
  Usage?: IUsage;
};


/**
 * Response format for SQL operations specifically to Views
 */
export interface IAPIResponse<T = any> {
  status: boolean;
  data?: T;
  payload?: T;
  error?: string;
  errorCode?: string | 'VIEW_NOT_WRITABLE' | 'VIEW_INSERT_ERROR' | 'VIEW_UPDATE_ERROR' | 'VIEW_DELETE_ERROR';
  affectedRows?: number;
  insertId?: number;
}
