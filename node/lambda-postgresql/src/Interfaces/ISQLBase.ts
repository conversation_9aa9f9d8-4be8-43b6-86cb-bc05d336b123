/**
 * Database connection credentials
 */
export interface ISQLCredentials {
  host: string;
  user: string;
  password: string;
  database?: string;
  port?: number;
  ssl?: any;
}

/**
 * Column information from database
 */
export interface IColumnInfo {
  column_name: string;
  data_type: string;
  is_nullable: "YES" | "NO";
  column_key: string;
  column_default: any;
  extra: string;
  column_comment?: string;
  character_maximum_length?: number;
  numeric_precision?: number;
  numeric_scale?: number;
}
