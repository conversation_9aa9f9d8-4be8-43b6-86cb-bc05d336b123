import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  APIGatewayProxyHandler,
} from "aws-lambda";
import {
  nocaResponse,
  responseBuilder,
  initPowertools,
  getParams,
  IEventParams,
} from "/opt/nodejs/commons";
import { META_METHODS } from "../Interfaces/ISQLHandler";
import { processMetaRequest } from "../routes/MetaRoute";
import { processDataRequest } from "../routes/DataRoute";

const lambdaHandler: APIGatewayProxyHandler = async (
  event: APIGatewayProxyEvent,
) => {
  const { logger } = initPowertools("lambda-postgresql");

  const aParams = await getParams(event.body as unknown as IEventParams);

  let requiredParams = ["payload", "credentials"];

  for (let param of requiredParams) {
    if (!aParams[param]) {
      logger.error(
        `Missing '${param}' parameter in body while trying to execute PostgreSQL operation`,
        {
          details: aParams,
        },
      );

      return await nocaResponse(
        {
          status: false,
          message: `Missing '${param}' parameter in body while trying to execute PostgreSQL operation`,
        },
        500,
        aParams,
      );
    }
  }

  try {
    const payload = aParams["payload"];

    let result;

    // Check if it's a meta operation (method-based) or data operation (action-based)
    if (payload.method && META_METHODS.includes(payload.method)) {
      result = await processMetaRequest(aParams);
    } else if (payload.action || payload.method) {
      if(payload.method === 'mcpSelect') {
        payload.action = 'select';
      }
      result = await processDataRequest(aParams);
    } else {
      return await nocaResponse(
        {
          status: false,
          error: "Either method or action is required in payload",
        },
        400,
        aParams,
      );
    }

    if (!result.status) {
      return await nocaResponse(result, 400, aParams);
    }

    const resultData: any = {
      status: true,
      payload: result.data,
    };

    if (result.affectedRows !== undefined) {
      resultData.affectedRows = result.affectedRows;
    }

    if (result.insertId !== undefined) {
      resultData.insertId = result.insertId;
    }

    return await nocaResponse(resultData, 200, aParams);
  } catch (error: any) {
    logger.error(
      `Unexpected error occurred while trying to process PostgreSQL operation: ${error}`,
    );
    return await nocaResponse(
      {
        status: false,
        error: String(error?.message ? error.message : error),
      },
      500,
      aParams,
    );
  }
};

export { lambdaHandler };
