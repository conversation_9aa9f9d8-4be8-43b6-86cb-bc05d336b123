import { IAPIResponse } from "../Interfaces/ISQLHandler";
import PostgreSQLObject from "../Services/PostgreSQLObject";

export const processDataRequest = async (
  aParams: any,
): Promise<IAPIResponse> => {
  const payload = aParams["payload"];
  const credentials = aParams["credentials"];

  // Handle new action-based requests (similar to Salesforce)
  try {
    // Validate required fields
    if (!payload.database || !payload.table) {
      return {
        status: false,
        error: "Database, table, and action are required",
        errorCode: "MISSING_REQUIRED_FIELDS",
      };
    }

    // Set database in credentials for connection
    const dbCredentials = { ...credentials, database: payload.database };

    const postgresqlObject = new PostgreSQLObject(dbCredentials, payload);

    const initRes = await postgresqlObject.initialize();
    if (!initRes.status) {
      return {
        status: false,
        error: initRes.error || "Failed to initialize database connection",
      };
    }

    const result = await postgresqlObject.execute();
    await postgresqlObject.close();

    if (!result.status) {
      return {
        status: false,
        error: result.error,
        errorCode: result.errorCode,
      };
    }

    return {
      status: true,
      data: result.data,
    };
  } catch (error) {
    return {
      status: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      errorCode: "UNEXPECTED_ERROR",
    };
  }
};
