import { IAPIResponse } from "../Interfaces/ISQLHandler";
import SQLMeta from "../Services/SQLMeta";

export const processMetaRequest = async (
  aParams: any,
): Promise<IAPIResponse> => {
  const payload = aParams["payload"];
  const method = payload["method"];
  const sqlMeta = new SQLMeta(aParams["credentials"]);

  const initRes = await sqlMeta.init();
  if (!initRes.status) {
    return {
      status: false,
      error: initRes.error,
    };
  }

  let result: any;

  try {
    switch (method) {
      case "getDatabases":
        result = await sqlMeta.listDatabases();
        break;

      case "getTables":
        if (!payload.database && !payload.selectedObjectType) {
          throw new Error("Database name and selectedObjectType is required for getTables");
        }
        result = await sqlMeta.listTables(payload.database, payload.selectedObjectType);
        break;

      case "getFieldsForTable":
        if (!payload.database || !payload.table) {
          throw new Error(
            "Database and table names are required for getFieldsForTable",
          );
        }
        const writeMode =
          payload.selectedAction &&
          ["create", "update", "upsert"].includes(payload.selectedAction);
        result = await sqlMeta.listColumns(
          payload.database,
          payload.table,
          writeMode,
        );
        break;

      case "getPrimaryKeys":
        if (!payload.database || !payload.table) {
          throw new Error(
            "Database and table names are required for getPrimaryKeys",
          );
        }
        result = await sqlMeta.getPrimaryKeys(payload.database, payload.table);
        break;

      default:
        throw new Error(`Unsupported meta method: ${method}`);
    }

    await sqlMeta.close();

    return {
      status: true,
      data: result,
    };
  } catch (error) {
    await sqlMeta.close();
    return {
      status: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};
