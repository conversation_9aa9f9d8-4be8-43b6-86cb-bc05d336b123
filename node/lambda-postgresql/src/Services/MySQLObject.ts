import SQLData from "./SQLData";
import { ISQLCredentials } from "../Interfaces/ISQLBase";
import { ISQLPayload, IResponseInterface } from "../Interfaces/ISQLHandler";
import SQLViewUtils from "./SQLViewUtils";


export default class MySQLObject extends SQLData {

  protected viewUtils: SQLViewUtils;
  constructor(credentials: ISQLCredentials, settings: ISQLPayload) {
    super(credentials, settings);
  }

  /**
   * Initialize the MySQL connection and return formatted response
   */
  async initialize(): Promise<IResponseInterface> {
    try {
      const initResult = await super.init();
      if (!initResult.status) {
        return {
          status: false,
          error: initResult.error || "Failed to initialize MySQL connection",
        };
      }
      return { status: true, data: "Connected successfully" };
    } catch (error) {
      return {
        status: false,
        error:
          error instanceof Error
            ? error.message
            : "Unknown initialization error",
      };
    }
  }

  /**
   * Execute the SQL operation based on action
   */
  async execute(): Promise<IResponseInterface> {
    try {
      this.viewUtils = new SQLViewUtils(this._credentials, this.settings);

      switch (this.settings?.action) {
        case 'mcpSelect':
        case "select":
          return await this.handleSelect();
        case "create":
          return await this.handleCreate();
        case 'update':
          return await this.handleUpdate();
        case 'upsert':
          return await this.handleUpsert();
        case 'delete':
          return await this.handleDelete();
        default:
          return {
            status: false,
            error: `Unsupported action: ${this.settings?.action}`,
            errorCode: "INVALID_ACTION",
          };
      }
    } catch (error) {
      return {
        status: false,
        error: error instanceof Error ? error.message : "Unknown execution error",
        errorCode: "EXECUTION_ERROR",
      };
    }
  }

  /**
   * Handle SELECT operations
   */
  private async handleSelect(): Promise<IResponseInterface> {
    try {
      const result = await this.advancedSelect();
      return {
        status: true,
        data: result.data,
      };
    } catch (error) {
      return {
        status: false,
        error: `Select operation failed: ${error instanceof Error ? error.message : error}`,
        errorCode: "SELECT_ERROR",
      };
    }
  }

  /**
   * Handle CREATE operations
   */
  private async handleCreate(): Promise<IResponseInterface> {
    try {
      const result = await this.advancedCreate();
      return {
        status: true,
        data: result.data[0],
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Check for view-related MySQL errors
      if (this.viewUtils.isViewError(errorMessage)) {
        return {
          status: false,
          error: `Cannot INSERT into view '${this.settings?.table}'. ${this.viewUtils.getViewErrorMessage(errorMessage)}`,
          errorCode: "VIEW_INSERT_ERROR",
        };
      }

      return {
        status: false,
        error: `Create operation failed: ${error instanceof Error ? error.message : error}`,
        errorCode: "CREATE_ERROR",
      };
    }
  }

  /**
   * Handle UPDATE operations
   */
  private async handleUpdate(): Promise<IResponseInterface> {
    try {
      const result = await this.advancedUpdate();
      return {
        status: true,
        data: result.data
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Check for view-related MySQL errors
      if (this.viewUtils.isViewError(errorMessage)) {
        return {
          status: false,
          error: `Cannot UPDATE into view '${this.settings?.table}'. ${this.viewUtils.getViewErrorMessage(errorMessage)}`,
          errorCode: "VIEW_UPDATE_ERROR",
        };
      }

      return {
        status: false,
        error: `Update operation failed: ${error instanceof Error ? error.message : error}`,
        errorCode: 'UPDATE_ERROR'
      };
    }
  }

  /**
   * Handle UPSERT operations (INSERT ... ON DUPLICATE KEY UPDATE)
   */
  private async handleUpsert(): Promise<IResponseInterface> {
    try {
      const result = await this.advancedUpsert();
      return {
        status: true,
        data: result.data[0]
      };
    } catch (error) {

        const errorMessage = error instanceof Error ? error.message : String(error);

        // Check for view-related MySQL errors
        if (this.viewUtils.isViewError(errorMessage)) {
          return {
            status: false,
            error: `Cannot UPSERT into view '${this.settings?.table}'. ${this.viewUtils.getViewErrorMessage(errorMessage)}`,
            errorCode: "VIEW_UPSERT_ERROR",
          };
        }

      return {
        status: false,
        error: `Upsert operation failed: ${error instanceof Error ? error.message : error}`,
        errorCode: 'UPSERT_ERROR'
      };
    }
  }

  /**
   * Handle DELETE operations
   */
  private async handleDelete(): Promise<IResponseInterface> {
    try {
      const result = await this.advancedDelete();
      return {
        status: true,
        data: result.data
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Check for view-related MySQL errors
      if (this.viewUtils.isViewError(errorMessage)) {
        return {
          status: false,
          error: `Cannot DELETE into view '${this.settings?.table}'. ${this.viewUtils.getViewErrorMessage(errorMessage)}`,
          errorCode: "VIEW_DELETE_ERROR",
        };
      }

      return {
        status: false,
        error: `Delete operation failed: ${error instanceof Error ? error.message : error}`,
        errorCode: 'DELETE_ERROR'
      };
    }
  }
}
