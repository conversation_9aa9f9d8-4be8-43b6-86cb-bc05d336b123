import { ISQLCredentials } from "../Interfaces/ISQLBase";
import { ISQLPayload } from "../Interfaces/ISQLHandler";
import SQLMeta from "./SQLMeta";

export default class SQLViewUtils {
  private _credentials: ISQLCredentials;
  protected settings?: ISQLPayload;

  constructor(credentials: ISQLCredentials, settings?: ISQLPayload) {
    this._credentials = credentials;
    this.settings = settings;
  }

  /**
   * Validate if the table is a view and if it's updatable
   * Enhanced to detect join views and multi-table scenarios
   */
  async validateTableForWriteOperations(): Promise<{ valid: boolean; error?: string; warning?: string }> {
    if (!this.settings?.database || !this.settings?.table) {
      return { valid: false, error: "Database and table are required" };
    }

    try {
      // Import SQLMeta for table type checking
      const sqlMeta = new SQLMeta(this._credentials);
      await sqlMeta.init();

      const tableType = await sqlMeta.getTableType(this.settings?.database, this.settings?.table);

      if (tableType === 'VIEW') {
        // Check if the view is updatable according to MySQL
        const isUpdatable = await sqlMeta.isViewUpdatable(this.settings?.database, this.settings?.table);

        if (!isUpdatable) {
          await sqlMeta.close();
          return {
            valid: false,
            error: `Cannot perform write operations on view '${this.settings?.table}' - view is not updatable. Views must be simple (single table, no aggregations, no DISTINCT, etc.) to support INSERT/UPDATE/DELETE operations.`
          };
        }

        // Additional check: Get view definition to detect joins and complexity
        const viewComplexity = await sqlMeta.analyzeViewComplexity(this.settings?.database, this.settings?.table);

        if (viewComplexity.hasJoins) {
          await sqlMeta.close();
          return {
            valid: false,
            error: `Cannot perform write operations on view '${this.settings?.table}' - view contains JOIN operations. Write operations on join views can cause "Cannot modify more than one base table through a join view" errors.`
          };
        }

        if (viewComplexity.hasMultipleTables) {
          await sqlMeta.close();
          return {
            valid: true,
            warning: `View '${this.settings?.table}' references multiple tables. Write operations may fail if they affect multiple base tables.`
          };
        }

        // Even if updatable, some operations might still fail
        console.warn(`Attempting write operation on view '${this.settings?.table}' - this may fail depending on view complexity`);
      }

      await sqlMeta.close();
      return { valid: true };

    } catch (error) {
      console.warn(`Could not validate table type for write operations:`, error);
      // If we can't determine the table type, allow the operation to proceed
      // The database will return an appropriate error if the operation fails
      return { valid: true };
    }
  }



  /**
   * Enhanced error detection including join-specific errors
   */
  isViewError(errorMessage: string): boolean {
    const viewErrorPatterns = [
      /can\'t update table.*in stored function\/trigger/i,
      /the target table.*of the.*is not updatable/i,
      /insert.*not supported/i,
      /view.*is not updatable/i,
      /cannot insert/i,
      /cannot update/i,
      /cannot delete/i,
      /can not modify more than one base table through a join view/i,  // Added this pattern
      /cannot modify more than one base table/i,
      /multiple-table.*not yet supported/i
    ];

    return viewErrorPatterns.some(pattern => pattern.test(errorMessage));
  }

  /**
   * Enhanced error message for view-related errors
   */
  getViewErrorMessage(originalError: string): string {
    if (/join view/i.test(originalError)) {
      return `This view contains JOINs between multiple tables. PostgreSQL doesn't allow write operations that would affect multiple base tables through a join view. Original error: ${originalError}`;
    }

    return `Views can only be modified if they are simple views (single table, no joins, aggregations, DISTINCT, GROUP BY, HAVING, UNION, etc.). Original error: ${originalError}`;
  }
}
