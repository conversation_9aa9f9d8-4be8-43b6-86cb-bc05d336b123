import { ISQLPayload, TCondition } from "../Interfaces/ISQLHandler";
import MCPQueryBuilder from "./MCPQueryBuilder";
import * as _ from "lodash";

/**
 * SQLQueryBuilder - Handles building SQL query components
 * Simple and focused on query construction
 */
export default class SQLQueryBuilder {
  private settings: ISQLPayload;
  private mcpQueryBuilder: MCPQueryBuilder;

  constructor(settings: ISQLPayload) {
    this.settings = settings;
    this.mcpQueryBuilder = new MCPQueryBuilder(settings);
  }

  /**
   * Get access to the MCP query builder
   */
  getMcpQueryBuilder(): MCPQueryBuilder {
    return this.mcpQueryBuilder;
  }

  /**
   * Build WHERE conditions from conditions array
   * Automatically detects and uses MCP query if available
   */
  buildConditions(): { whereClause: string; params: any[] } {
    let whereClause = "";
    const params: any[] = [];

    try {
      const conditions: string[] = [];

      // Check if we have mcpQuery first - delegate to MCPQueryBuilder
      if (this.mcpQueryBuilder.hasMcpQuery()) {
        const mcpQueryResult =  this.mcpQueryBuilder.buildMcpConditions();
        params.push(...mcpQueryResult.params);
        conditions.push(...mcpQueryResult.conditions);
        whereClause = `WHERE ${conditions.join(" ")}`;
      }

      // Fall back to regular conditions
      if (!this.settings?.conditions || !this.settings.conditions.length) {
        return { whereClause, params };
      }

      _.each(this.settings.conditions, (row, index) => {
        if (!row.Field || row.Value === undefined) {
          return;
        }

        const isLast = index === this.settings!.conditions!.length - 1;
        const conditionStr = this.buildQueryCondition(row, isLast, params);

        if (conditionStr) {
          conditions.push(conditionStr);
        }
      });

      if (conditions.length) {
        whereClause = `WHERE ${conditions.join(" ")}`;
      }
    } catch (err) {
      console.error(`Build Conditions Error: ${err}`);
    }

    return { whereClause, params };
  }

  /**
   * Build individual query condition
   */
  private buildQueryCondition(
    row: TCondition,
    isLast: boolean,
    params: any[],
  ): string {
    try {
      const typeNotNeedQuote = [
        "boolean",
        "double",
        "decimal",
        "float",
        "number",
        "int",
        "bigint",
      ];
      let fieldName = row.Field;
      let val = row.Value;

      let operator = "";

      switch (row.Condition) {
        case "equals":
          if (Array.isArray(row.Value)) {
            const placeholders = row.Value.map(() => "?").join(",");
            operator = `IN (${placeholders})`;
            params.push(...row.Value);
          } else {
            operator = "= ?";
            params.push(val);
          }
          break;
        case "notEqual":
          operator = "!= ?";
          params.push(val);
          break;
        case "lessThan":
          operator = "< ?";
          params.push(val);
          break;
        case "greaterThan":
          operator = "> ?";
          params.push(val);
          break;
        case "lessOrEqual":
          operator = "<= ?";
          params.push(val);
          break;
        case "greaterOrEqual":
          operator = ">= ?";
          params.push(val);
          break;
        case "contains":
          operator = "LIKE ?";
          params.push(`%${val}%`);
          break;
        case "notContain":
          operator = "NOT LIKE ?";
          params.push(`%${val}%`);
          break;
        case "startsWith":
          operator = "LIKE ?";
          params.push(`${val}%`);
          break;
        case "endsWith":
          operator = "LIKE ?";
          params.push(`%${val}`);
          break;
        case "isEmpty":
          if (typeNotNeedQuote.includes(row.Type || "")) {
            operator = "IS NULL";
          } else {
            operator = "= ''";
          }
          break;
        case "isNotEmpty":
          if (typeNotNeedQuote.includes(row.Type || "")) {
            operator = "IS NOT NULL";
          } else {
            operator = "!= ''";
          }
          break;
        default:
          operator = "= ?";
          params.push(val);
          break;
      }

      let postCondition = "";
      if (!isLast) {
        if(row.Post)
          postCondition = ` ${row.Post.toUpperCase()} `;
        else
          postCondition = ` AND `;
      }

      return `${fieldName} ${operator}${postCondition}`;
    } catch (err) {
      console.error(`Build Query Condition Error: ${err}`);
      return "";
    }
  }

  /**
   * Build ORDER BY clause
   */
  buildSort(): string {
    let result = "";

    try {
      if (!this.settings?.sort || !this.settings.sort.length) {
        return result;
      }

      const sortClauses: string[] = [];

      _.each(this.settings.sort, (row) => {
        if (!row.Field) {
          return;
        }

        const direction = row.Sort === "descend" ? "DESC" : "ASC";
        sortClauses.push(`${row.Field} ${direction}`);
      });

      if (sortClauses.length) {
        result = `ORDER BY ${sortClauses.join(", ")}`;
      }
    } catch (err) {
      console.error(`Build Sort Error: ${err}`);
    }

    return result;
  }

  /**
   * Build LIMIT clause
   */
  buildLimit(): string {
    let limit = 0;

    // Check for mcpLimit first - delegate to MCPQueryBuilder
    const mcpLimit = this.mcpQueryBuilder.getMcpLimit();
    if (mcpLimit) {
      limit = mcpLimit;
    }
    // Fall back to regular limit logic
    else if (!this.settings?.fetch || this.settings.fetch === "first") {
      limit = 1;
    } else if (this.settings.fetch === "limits") {
      limit = this.settings.limit || 20;
    }

    return limit > 0 ? `LIMIT ${limit}` : "";
  }
}
