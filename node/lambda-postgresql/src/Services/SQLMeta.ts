import SQLBase from "./SQLBase";
import { ISQLCredentials } from "../Interfaces/ISQLBase";
import { ITableInfo } from "../Interfaces/ISQLMeta";
import { IColumnInfo } from "../Interfaces/ISQLBase";

export default class S<PERSON><PERSON>eta extends SQLBase {
  constructor(credentials: ISQLCredentials) {
    super(credentials);
  }

  /**
   * Convert MySQL-style query with ? placeholders to PostgreSQL-style with $1, $2, etc.
   */
  private convertToPostgreSQLQuery(query: string): string {
    let paramIndex = 1;
    return query.replace(/\?/g, () => `$${paramIndex++}`);
  }

  async getTableType(database: string, table: string): Promise<'TABLE' | 'VIEW' | 'UNKNOWN'> {
    let query = `SELECT table_type
    FROM information_schema.tables
    WHERE table_schema = ? AND table_name = ?
    LIMIT 1
  `;

    try {
      query = this.convertToPostgreSQLQuery(query);
      const result = await this.executeQuery<{ table_type: string }>(query, [database, table]);
      if (result && result.length > 0) {
        return result[0].table_type === 'VIEW' ? 'VIEW' : 'TABLE';
      }
      return 'UNKNOWN';
    } catch (error) {
      console.warn(`Could not determine table type for ${table}:`, error);
      return 'UNKNOWN';
    }
  }

  async isViewUpdatable(database: string, viewName: string): Promise<boolean> {
    const query = `SELECT IS_UPDATABLE
    FROM INFORMATION_SCHEMA.VIEWS
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
    LIMIT 1
  `;

    try {
      const result = await this.executeQuery<{ IS_UPDATABLE: string }>(query, [database, viewName]);
      if (result && result.length > 0) {
        return result[0].IS_UPDATABLE === 'YES';
      }
      return false;
    } catch (error) {
      console.warn(`Could not check if view ${viewName} is updatable:`, error);
      return false;
    }
  }

  /**
   * Analyze view complexity to detect potential issues with write operations
   */
  async analyzeViewComplexity(database: string, viewName: string): Promise<{
    hasJoins: boolean;
    hasMultipleTables: boolean;
    viewDefinition?: string;
  }> {
    try {
      const query = `      SELECT VIEW_DEFINITION
      FROM INFORMATION_SCHEMA.VIEWS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      LIMIT 1
    `;

      const result = await this.executeQuery<{ VIEW_DEFINITION: string }>(query, [database, viewName]);

      if (!result || result.length === 0) {
        return { hasJoins: false, hasMultipleTables: false };
      }

      const viewDefinition = result[0].VIEW_DEFINITION.toUpperCase();

      // Check for JOIN operations
      const joinPatterns = [
        /\bJOIN\b/,
        /\bINNER\s+JOIN\b/,
        /\bLEFT\s+JOIN\b/,
        /\bRIGHT\s+JOIN\b/,
        /\bFULL\s+JOIN\b/,
        /\bCROSS\s+JOIN\b/
      ];

      const hasJoins = joinPatterns.some(pattern => pattern.test(viewDefinition));

      // Check for multiple table references (simple heuristic)
      // Look for multiple FROM clauses or comma-separated tables
      const tableReferencePatterns = [
        /\bFROM\s+\w+\s*,/,  // FROM table1, table2
        /,\s*\w+\s+\w+/      // Multiple table aliases
      ];

      const hasMultipleTables = hasJoins || tableReferencePatterns.some(pattern => pattern.test(viewDefinition));

      return {
        hasJoins,
        hasMultipleTables,
        viewDefinition: result[0].VIEW_DEFINITION
      };

    } catch (error) {
      console.warn(`Could not analyze view complexity for ${viewName}:`, error);
      return { hasJoins: false, hasMultipleTables: false };
    }
  }

  async listTables(database: string, selectedObjectType: string): Promise<any[]> {

    const tableType = selectedObjectType === 'table' ? 'BASE TABLE' : 'VIEW';
    let query = `    SELECT
                         table_name as table_name,
                         '' as table_comment,
                         table_schema as table_schema,
                         table_type as table_type
                       FROM
                         information_schema.tables
                       WHERE
                         table_schema = ?
                         AND table_type = '${tableType}'
                       ORDER BY
                         table_type, table_name;
    `;

    query = this.convertToPostgreSQLQuery(query);
    const result = await this.executeQuery<ITableInfo & { table_type: string }>(query, [database]);

    const formattedResults = await Promise.all(
        result.map(async (row) => {
          return {
            label: row.table_name,
            value: row.table_name
          };
        })
    );

    return formattedResults;
  }

  async listDatabases(): Promise<any[]> {
    const query = `
      SELECT schema_name as schema_name
      FROM information_schema.schemata
      WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1');
    `;

    const result = await this.executeQuery<{ schema_name: string }>(query);
    return result.map((row) => ({
      label: row.schema_name,
      value: row.schema_name,
    }));
  }
  async listColumns(
    database: string,
    table: string,
    writableColumnsOnly?: boolean,
  ): Promise<any[]> {
    let query = `
      SELECT
        column_name as column_name,
        data_type as data_type,
        is_nullable as is_nullable,
        '' as column_key,
        column_default as column_default,
        '' as extra,
        '' as column_comment,
        character_maximum_length as character_maximum_length,
        numeric_precision as numeric_precision,
        numeric_scale as numeric_scale
      FROM
        information_schema.columns
      WHERE
        table_schema = ?
        AND table_name = ?
      ORDER BY
        ordinal_position;
    `;

    query = this.convertToPostgreSQLQuery(query);
    let objectMeta = await this.executeQuery<IColumnInfo>(query, [
      database,
      table,
    ]);
    // Filter out primary key columns if writableColumnsOnly is true
    // if(writableColumnsOnly) {
    //   objectMeta = objectMeta.filter((col: IColumnInfo) => col.column_key !== 'PRI');
    // }

    return objectMeta.map((col: IColumnInfo) => ({
      label: col.column_name,
      value: col.column_name,
      type: col.data_type,
      defaultValue: col.column_default,
      maxLength:
        col.character_maximum_length ||
        (col.numeric_precision ? col.numeric_precision : null),
      // Additional properties that might be needed
      required: col.column_key !== "PRI" && col.is_nullable === "NO",
      numericScale: col.numeric_scale,
    }));
  }

  async getPrimaryKeys(database: string, table: string): Promise<string[]> {
    const query = `
      SELECT
        COLUMN_NAME as column_name
      FROM
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE
        TABLE_SCHEMA = ?
        AND TABLE_NAME = ?
        AND CONSTRAINT_NAME = 'PRIMARY'
      ORDER BY
        ORDINAL_POSITION;
    `;

    const result = await this.executeQuery<{ column_name: string }>(query, [
      database,
      table,
    ]);
    return result.map((col) => col.column_name);
  }
}
