import SQLBase from "./SQLBase";
import { ISQLCredentials } from "../Interfaces/ISQLBase";
import { ITableInfo } from "../Interfaces/ISQLMeta";
import { IColumnInfo } from "../Interfaces/ISQLBase";

export default class S<PERSON><PERSON><PERSON> extends SQLBase {
  constructor(credentials: ISQLCredentials) {
    super(credentials);
  }

  async getTableType(database: string, table: string): Promise<'TABLE' | 'VIEW' | 'UNKNOWN'> {
    const query = `SELECT TABLE_TYPE
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
    LIMIT 1
  `;

    try {
      const result = await this.executeQuery<{ TABLE_TYPE: string }>(query, [database, table]);
      if (result && result.length > 0) {
        return result[0].TABLE_TYPE === 'VIEW' ? 'VIEW' : 'TABLE';
      }
      return 'UNKNOWN';
    } catch (error) {
      console.warn(`Could not determine table type for ${table}:`, error);
      return 'UNKNOWN';
    }
  }

  async isViewUpdatable(database: string, viewName: string): Promise<boolean> {
    const query = `SELECT IS_UPDATABLE
    FROM INFORMATION_SCHEMA.VIEWS
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
    LIMIT 1
  `;

    try {
      const result = await this.executeQuery<{ IS_UPDATABLE: string }>(query, [database, viewName]);
      if (result && result.length > 0) {
        return result[0].IS_UPDATABLE === 'YES';
      }
      return false;
    } catch (error) {
      console.warn(`Could not check if view ${viewName} is updatable:`, error);
      return false;
    }
  }

  /**
   * Analyze view complexity to detect potential issues with write operations
   */
  async analyzeViewComplexity(database: string, viewName: string): Promise<{
    hasJoins: boolean;
    hasMultipleTables: boolean;
    viewDefinition?: string;
  }> {
    try {
      const query = `      SELECT VIEW_DEFINITION
      FROM INFORMATION_SCHEMA.VIEWS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      LIMIT 1
    `;

      const result = await this.executeQuery<{ VIEW_DEFINITION: string }>(query, [database, viewName]);

      if (!result || result.length === 0) {
        return { hasJoins: false, hasMultipleTables: false };
      }

      const viewDefinition = result[0].VIEW_DEFINITION.toUpperCase();

      // Check for JOIN operations
      const joinPatterns = [
        /\bJOIN\b/,
        /\bINNER\s+JOIN\b/,
        /\bLEFT\s+JOIN\b/,
        /\bRIGHT\s+JOIN\b/,
        /\bFULL\s+JOIN\b/,
        /\bCROSS\s+JOIN\b/
      ];

      const hasJoins = joinPatterns.some(pattern => pattern.test(viewDefinition));

      // Check for multiple table references (simple heuristic)
      // Look for multiple FROM clauses or comma-separated tables
      const tableReferencePatterns = [
        /\bFROM\s+\w+\s*,/,  // FROM table1, table2
        /,\s*\w+\s+\w+/      // Multiple table aliases
      ];

      const hasMultipleTables = hasJoins || tableReferencePatterns.some(pattern => pattern.test(viewDefinition));

      return {
        hasJoins,
        hasMultipleTables,
        viewDefinition: result[0].VIEW_DEFINITION
      };

    } catch (error) {
      console.warn(`Could not analyze view complexity for ${viewName}:`, error);
      return { hasJoins: false, hasMultipleTables: false };
    }
  }

  async listTables(database: string, selectedObjectType: string): Promise<any[]> {

    const tableType = selectedObjectType === 'table' ? 'BASE TABLE' : 'VIEW';
    const query = `    SELECT
                         TABLE_NAME as table_name,
                         TABLE_COMMENT as table_comment,
                         TABLE_SCHEMA as table_schema,
                         TABLE_TYPE as table_type
                       FROM
                         INFORMATION_SCHEMA.TABLES
                       WHERE
                         TABLE_SCHEMA = ?
                         AND TABLE_TYPE = '${tableType}'
                       ORDER BY
                         TABLE_TYPE, TABLE_NAME;
    `;

    const result = await this.executeQuery<ITableInfo & { table_type: string }>(query, [database]);

    const formattedResults = await Promise.all(
        result.map(async (row) => {
          return {
            label: row.table_name,
            value: row.table_name
          };
        })
    );

    return formattedResults;
  }

  async listDatabases(): Promise<any[]> {
    const query = `
      SELECT SCHEMA_NAME as schema_name
      FROM information_schema.schemata
      WHERE SCHEMA_NAME NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys');
    `;

    const result = await this.executeQuery<{ schema_name: string }>(query);
    return result.map((row) => ({
      label: row.schema_name,
      value: row.schema_name,
    }));
  }
  async listColumns(
    database: string,
    table: string,
    writableColumnsOnly?: boolean,
  ): Promise<any[]> {
    const query = `
      SELECT
        COLUMN_NAME as column_name,
        DATA_TYPE as data_type,
        IS_NULLABLE as is_nullable,
        COLUMN_KEY as column_key,
        COLUMN_DEFAULT as column_default,
        EXTRA as extra,
        COLUMN_COMMENT as column_comment,
        CHARACTER_MAXIMUM_LENGTH as character_maximum_length,
        NUMERIC_PRECISION as numeric_precision,
        NUMERIC_SCALE as numeric_scale
      FROM
        INFORMATION_SCHEMA.COLUMNS
      WHERE
        TABLE_SCHEMA = ?
        AND TABLE_NAME = ?
      ORDER BY
        ORDINAL_POSITION;
    `;

    let objectMeta = await this.executeQuery<IColumnInfo>(query, [
      database,
      table,
    ]);
    // Filter out primary key columns if writableColumnsOnly is true
    // if(writableColumnsOnly) {
    //   objectMeta = objectMeta.filter((col: IColumnInfo) => col.column_key !== 'PRI');
    // }

    return objectMeta.map((col: IColumnInfo) => ({
      label: col.column_name,
      value: col.column_name,
      type: col.data_type,
      defaultValue: col.column_default,
      maxLength:
        col.character_maximum_length ||
        (col.numeric_precision ? col.numeric_precision : null),
      // Additional properties that might be needed
      required: col.column_key !== "PRI" && col.is_nullable === "NO",
      numericScale: col.numeric_scale,
    }));
  }

  async getPrimaryKeys(database: string, table: string): Promise<string[]> {
    const query = `
      SELECT
        COLUMN_NAME as column_name
      FROM
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE
        TABLE_SCHEMA = ?
        AND TABLE_NAME = ?
        AND CONSTRAINT_NAME = 'PRIMARY'
      ORDER BY
        ORDINAL_POSITION;
    `;

    const result = await this.executeQuery<{ column_name: string }>(query, [
      database,
      table,
    ]);
    return result.map((col) => col.column_name);
  }
}
