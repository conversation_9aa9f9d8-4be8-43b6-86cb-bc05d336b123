import SQLBase from "./SQLBase";
import { ISQLCredentials } from "../Interfaces/ISQLBase";
import { ISQLPayload } from "../Interfaces/ISQLHandler";

/**
 * SQLDatabaseUtils - Handles database utility functions
 */
export default class SQLDatabaseUtils extends SQLBase {
  private settings: ISQLPayload;

  constructor(credentials: ISQLCredentials, settings: ISQLPayload) {
    super(credentials);
    this.settings = settings;
  }

  /**
   * Get the primary key column name for a table
   */
  async getPrimaryKeyColumn(): Promise<string | null> {
    if (!this.settings?.table) {
      return null;
    }

    try {
      // Query to get primary key information
      const pkQuery = `
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = ?
        AND CONSTRAINT_NAME = 'PRIMARY'
        ORDER BY ORDINAL_POSITION
        LIMIT 1
      `;

      const result = await this.executeQuery(pkQuery, [this.settings.table]);
      if (result && result.length > 0) {
        return (result[0] as any).COLUMN_NAME;
      }
    } catch (error) {
      console.warn("Could not detect primary key column:", error);
    }

    return null;
  }

  /**
   * Try to fetch a record by insertId using various strategies
   */
  async fetchRecordByInsertId(insertId: number): Promise<any> {
    if (!this.settings?.table || !insertId) {
      return null;
    }

    // Strategy 1: Try to get the actual primary key column name
    const primaryKeyColumn = await this.getPrimaryKeyColumn();
    if (primaryKeyColumn) {
      try {
        const selectQuery = `SELECT * FROM ${this.settings.table} WHERE ${primaryKeyColumn} = ? LIMIT 1`;
        const records = await this.executeQuery(selectQuery, [insertId]);
        if (records && records.length > 0) {
          return records[0];
        }
      } catch (e) {
        console.warn(
          `Failed to fetch using detected primary key '${primaryKeyColumn}':`,
          e,
        );
      }
    }

    // Strategy 2: Try common primary key column names
    const pkColumns = ["id", "ID", "pk", "primary_key", "key", "Key"];

    for (const pkCol of pkColumns) {
      try {
        const selectQuery = `SELECT * FROM ${this.settings.table} WHERE ${pkCol} = ? LIMIT 1`;
        const records = await this.executeQuery(selectQuery, [insertId]);
        if (records && records.length > 0) {
          return records[0];
        }
      } catch (e) {
        continue;
      }
    }

    // Strategy 3: Try to get the last inserted record (MySQL specific)
    try {
      const lastRecordQuery = `SELECT * FROM ${this.settings.table} ORDER BY ${primaryKeyColumn || "id"} DESC LIMIT 1`;
      const records = await this.executeQuery(lastRecordQuery, []);
      if (records && records.length > 0) {
        return records[0];
      }
    } catch (e) {
      // Final fallback failed
    }

    return null;
  }
}
