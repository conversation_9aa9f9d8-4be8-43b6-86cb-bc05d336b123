import SQLBase from "./SQLBase";
import { ISQLCredentials } from "../Interfaces/ISQLBase";
import { ISQLPayload } from "../Interfaces/ISQLHandler";

/**
 * SQLDatabaseUtils - Handles database utility functions
 */
export default class SQLDatabaseUtils extends SQLBase {
  private settings: ISQLPayload;

  constructor(credentials: ISQLCredentials, settings: ISQLPayload) {
    super(credentials);
    this.settings = settings;
  }

  /**
   * Convert MySQL-style query with ? placeholders to PostgreSQL-style with $1, $2, etc.
   */
  private convertToPostgreSQLQuery(query: string): string {
    let paramIndex = 1;
    return query.replace(/\?/g, () => `$${paramIndex++}`);
  }

  /**
   * Get the primary key column name for a table
   */
  async getPrimaryKeyColumn(): Promise<string | null> {
    if (!this.settings?.table) {
      return null;
    }

    try {
      // Query to get primary key information for PostgreSQL
      let pkQuery = `
        SELECT column_name
        FROM information_schema.key_column_usage
        WHERE table_schema = ?
        AND table_name = ?
        AND constraint_name IN (
          SELECT constraint_name
          FROM information_schema.table_constraints
          WHERE table_schema = ?
          AND table_name = ?
          AND constraint_type = 'PRIMARY KEY'
        )
        ORDER BY ordinal_position
        LIMIT 1
      `;

      pkQuery = this.convertToPostgreSQLQuery(pkQuery);
      const result = await this.executeQuery(pkQuery, [
        this.settings.database || 'public',
        this.settings.table,
        this.settings.database || 'public',
        this.settings.table
      ]);
      if (result && result.length > 0) {
        return (result[0] as any).column_name;
      }
    } catch (error) {
      console.warn("Could not detect primary key column:", error);
    }

    return null;
  }

  /**
   * Try to fetch a record by insertId using various strategies
   */
  async fetchRecordByInsertId(insertId: number): Promise<any> {
    if (!this.settings?.table || !insertId) {
      return null;
    }

    // Strategy 1: Try to get the actual primary key column name
    const primaryKeyColumn = await this.getPrimaryKeyColumn();
    if (primaryKeyColumn) {
      try {
        let selectQuery = `SELECT * FROM ${this.settings.table} WHERE ${primaryKeyColumn} = ? LIMIT 1`;
        selectQuery = this.convertToPostgreSQLQuery(selectQuery);
        const records = await this.executeQuery(selectQuery, [insertId]);
        if (records && records.length > 0) {
          return records[0];
        }
      } catch (e) {
        console.warn(
          `Failed to fetch using detected primary key '${primaryKeyColumn}':`,
          e,
        );
      }
    }

    // Strategy 2: Try common primary key column names
    const pkColumns = ["id", "ID", "pk", "primary_key", "key", "Key"];

    for (const pkCol of pkColumns) {
      try {
        let selectQuery = `SELECT * FROM ${this.settings.table} WHERE ${pkCol} = ? LIMIT 1`;
        selectQuery = this.convertToPostgreSQLQuery(selectQuery);
        const records = await this.executeQuery(selectQuery, [insertId]);
        if (records && records.length > 0) {
          return records[0];
        }
      } catch (e) {
        continue;
      }
    }

    // Strategy 3: Try to get the last inserted record
    try {
      const lastRecordQuery = `SELECT * FROM ${this.settings.table} ORDER BY ${primaryKeyColumn || "id"} DESC LIMIT 1`;
      const records = await this.executeQuery(lastRecordQuery, []);
      if (records && records.length > 0) {
        return records[0];
      }
    } catch (e) {
      // Final fallback failed
    }

    return null;
  }
}
