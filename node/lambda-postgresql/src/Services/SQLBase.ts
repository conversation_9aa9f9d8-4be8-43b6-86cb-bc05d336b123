import { createPool, Pool, PoolOptions } from "mysql2/promise";
import { ISQLCredentials } from "../Interfaces/ISQLBase";

export default class SQLBase {
  protected _pool: Pool;
  protected _credentials: ISQLCredentials;

  constructor(credentials: ISQLCredentials) {
    this._credentials = credentials;

    const poolConfig: PoolOptions = {
      host: credentials.host,
      user: credentials.user,
      password: credentials.password,
      database: credentials.database,
      port: credentials.port || 3306,
      ssl: credentials.ssl,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
    };

    this._pool = createPool(poolConfig);
  }

  async init(): Promise<{ status: boolean; error?: string }> {
    try {
      const connection = await this._pool.getConnection();
      await connection.ping();
      connection.release();
      return { status: true };
    } catch (error) {
      console.error("Failed to connect to MySQL server", { error });
      return {
        status: false,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  async executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {
    const connection = await this._pool.getConnection();
    try {
      const [rows] = await connection.query(query, params);
      return rows as T[];
    } finally {
      connection.release();
    }
  }

  async close(): Promise<void> {
    await this._pool.end();
  }
}
