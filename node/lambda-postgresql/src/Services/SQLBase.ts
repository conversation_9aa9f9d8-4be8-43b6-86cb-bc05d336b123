import { Pool, PoolConfig } from "pg";
import { ISQLCredentials } from "../Interfaces/ISQLBase";

export default class SQLBase {
  protected _pool: Pool;
  protected _credentials: ISQLCredentials;

  constructor(credentials: ISQLCredentials) {
    this._credentials = credentials;

    const poolConfig: PoolConfig = {
      host: credentials.host,
      user: credentials.user,
      password: credentials.password,
      database: credentials.database,
      port: credentials.port || 5432,
      ssl: credentials.ssl,
      max: 10, // maximum number of clients in the pool
      idleTimeoutMillis: 30000, // how long a client is allowed to remain idle before being closed
      connectionTimeoutMillis: 2000, // how long to wait when connecting a new client
    };

    this._pool = new Pool(poolConfig);
  }

  async init(): Promise<{ status: boolean; error?: string }> {
    try {
      const client = await this._pool.connect();
      await client.query('SELECT 1'); // Simple test query for PostgreSQL
      client.release();
      return { status: true };
    } catch (error) {
      console.error("Failed to connect to PostgreSQL server", { error });
      return {
        status: false,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  async executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {
    const client = await this._pool.connect();
    try {
      const result = await client.query(query, params);
      return result.rows as T[];
    } finally {
      client.release();
    }
  }

  async close(): Promise<void> {
    await this._pool.end();
  }
}
