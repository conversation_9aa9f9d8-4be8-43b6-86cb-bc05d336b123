import { ISQLPayload, TMcpQuery } from "../Interfaces/ISQLHandler";
import * as _ from "lodash";

/**
 * MCPQueryBuilder - Handles MCP query building functionality
 * Simple and focused on MCP query construction
 */
export default class MCPQueryBuilder {
  private settings: ISQLPayload;

  constructor(settings: ISQLPayload) {
    this.settings = settings;
  }

  /**
   * Check if MCP query is available
   */
  hasMcpQuery(): boolean {
    return !!(this.settings?.mcpQuery && this.settings.mcpQuery.length > 0);
  }

  /**
   * Build MCP Query conditions from mcpQuery array
   */
  buildMcpConditions(): { conditions: string[]; params: any[] } {

    const params: any[] = [];
    const conditions: string[] = [];
    try {

      if (!this.settings?.mcpQuery || !this.settings.mcpQuery.length) {
        return { conditions, params };
      }

      _.each(this.settings.mcpQuery, (row, index) => {
        if (!row.field || row.value === undefined) {
          return;
        }

        const isLast = index === this.settings!.mcpQuery!.length - 1;
        const conditionStr = this.buildMcpQueryCondition(row, isLast, params);
        if (conditionStr) {
          conditions.push(conditionStr);
        }
      });

    } catch (err) {
      console.error(`Build MCP Conditions Error: ${err}`);
    }

    return { conditions, params };
  }

  /**
   * Build individual MCP query condition
   */
  private buildMcpQueryCondition(
    row: TMcpQuery,
    isLast: boolean,
    params: any[],
  ): string {
    try {
      let fieldName = row.field;
      let val = row.value;
      let operator = "";

      switch (row.operator.toLowerCase()) {
        case "equals":
        case "=":
          operator = "= ?";
          params.push(val);
          break;
        case "notequal":
        case "!=":
        case "<>":
          operator = "!= ?";
          params.push(val);
          break;
        case "lessthan":
        case "<":
          operator = "< ?";
          params.push(val);
          break;
        case "greaterthan":
        case ">":
          operator = "> ?";
          params.push(val);
          break;
        case "lessorequal":
        case "<=":
          operator = "<= ?";
          params.push(val);
          break;
        case "greaterorequal":
        case ">=":
          operator = ">= ?";
          params.push(val);
          break;
        case "contains":
        case "like":
          operator = "LIKE ?";
          params.push(`%${val}%`);
          break;
        case "startswith":
          operator = "LIKE ?";
          params.push(`${val}%`);
          break;
        case "endswith":
          operator = "LIKE ?";
          params.push(`%${val}`);
          break;
        case "isnull":
        case "isempty":
          operator = "IS NULL";
          break;
        case "isnotnull":
        case "isnotempty":
          operator = "IS NOT NULL";
          break;
        default:
          operator = "= ?";
          params.push(val);
          break;
      }

      // Add AND between conditions (no OR support in MCP for now)
      let postCondition = "";
      if (!isLast) {
        if(row.post)
        postCondition = ` ${row.post.toUpperCase()} `;
        else
          postCondition = ` AND `;
      }

      return `${fieldName} ${operator}${postCondition}`;
    } catch (err) {
      console.error(`Build MCP Query Condition Error: ${err}`);
      return "";
    }
  }

  /**
   * Get MCP limit value
   */
  getMcpLimit(): number | null {
    return this.settings?.mcpLimit || null;
  }

  /**
   * Check if this is a count query
   */
  isCountQuery(): boolean {
    return this.settings?.mcpAction === "count";
  }

  /**
   * Check if this is a select query
   */
  isSelectQuery(): boolean {
    return this.settings?.mcpAction === "select";
  }
}
