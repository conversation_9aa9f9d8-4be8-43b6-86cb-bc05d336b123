import SQLBase from "./SQLBase";
import { ISQLCredentials } from "../Interfaces/ISQLBase";
import { IQueryResult } from "../Interfaces/ISQLMeta";
import { ISQLPayload } from "../Interfaces/ISQLHandler";
import SQLQueryBuilder from "./SQLQueryBuilder";
import SQLDataFormatter from "./SQLDataFormatter";
import SQLDatabaseUtils from "./SQLDatabaseUtils";
import * as _ from "lodash";

/**
 * SQLData - Focused on core SQL operations only
 * Uses helper classes for other functionality
 */
export default class SQLData extends SQLBase {
  protected settings?: ISQLPayload;
  protected queryBuilder: SQLQueryBuilder;
  protected dataFormatter: SQLDataFormatter;
  protected databaseUtils: SQLDatabaseUtils;

  constructor(credentials: ISQLCredentials, settings?: ISQLPayload) {
    super(credentials);
    this.settings = settings;


    // Initialize helper classes
    if (this.settings) {
      this.queryBuilder = new SQLQueryBuilder(this.settings);
      this.dataFormatter = new SQLDataFormatter(this.settings);
      this.databaseUtils = new SQLDatabaseUtils(credentials, this.settings);
    }
  }

  /**
   * Convert MySQL-style query with ? placeholders to PostgreSQL-style with $1, $2, etc.
   */
  private convertToPostgreSQLQuery(query: string): string {
    let paramIndex = 1;
    return query.replace(/\?/g, () => `$${paramIndex++}`);
  }


  /**
   * Advanced select - handles both regular select and MCP queries with count support
   */
  async advancedSelect(): Promise<IQueryResult> {
    try {
      if (!this.settings?.table) {
        throw new Error("Table name is required");
      }

      const { whereClause, params } = this.queryBuilder.buildConditions();
      const sortClause = this.queryBuilder.buildSort();
      const limitClause = this.queryBuilder.buildLimit();

      // Determine if this is a count query using MCPQueryBuilder
      const mcpQueryBuilder = this.queryBuilder.getMcpQueryBuilder();
      const isCountQuery = mcpQueryBuilder.isCountQuery();

      // Determine if specific fields are specified
      const hasSpecificFields = this.settings?.fields && this.settings.fields.length > 0;

      // Build the SELECT clause based on mcpAction
      let selectClause = "*";
      if (isCountQuery) {
        selectClause = "COUNT(*) as count";
      } else if (hasSpecificFields) {
        selectClause = this.settings.fields?.join(", ") as string;
      }

      let query = `
        SELECT ${selectClause} FROM ${this.settings.table}
        ${whereClause}
        ${isCountQuery ? "" : sortClause}
        ${isCountQuery ? "" : limitClause}
      `
        .trim()
        .replace(/\s+/g, " ");

      query = this.convertToPostgreSQLQuery(query);
      const result = await this.executeQuery(query, params);

      return {
        data: result as any[],
      };
    } catch (error) {
      throw new Error(
        `Select operation failed: ${error instanceof Error ? error.message : error}`,
      );
    }
  }





  /**
   * Advanced create with field mapping
   */
  async advancedCreate(): Promise<IQueryResult> {
    try {
      if (!this.settings?.table) {
        throw new Error("Table name is required");
      }

      const data = this.dataFormatter.buildPushData();

      if (_.isEmpty(data)) {
        throw new Error("No data provided for create operation");
      }

      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = columns.map((_, index) => `$${index + 1}`).join(", ");

      const query = `
        INSERT INTO ${this.settings.table} (${columns.join(", ")})
        VALUES (${placeholders})
        RETURNING *
      `;

      const insertResult = await this.executeQuery(query, values);
      const createdRecord = insertResult.length > 0 ? insertResult[0] : null;

      return {
        data: createdRecord ? [createdRecord] : [data],
        affectedRows: insertResult.length,
        insertId: createdRecord?.id || null,
      };
    } catch (error) {
      throw new Error(
        `Create operation failed: ${error instanceof Error ? error.message : error}`,
      );
    }
  }

  /**
   * Advanced update with conditions
   */
  async advancedUpdate(): Promise<IQueryResult> {
    try {
      if (!this.settings?.table) {
        throw new Error("Table name is required");
      }

      const data = this.dataFormatter.buildPushData();

      if (_.isEmpty(data)) {
        throw new Error("No data provided for update operation");
      }

      const { whereClause, params: whereParams } = this.queryBuilder.buildConditions();

      if (!whereClause) {
        throw new Error("WHERE conditions are required for update operation");
      }

      const setClauses = Object.keys(data).map((key) => `${key} = ?`);
      const setParams = Object.values(data);

      let query = `
        UPDATE ${this.settings.table}
        SET ${setClauses.join(", ")}
        ${whereClause}
      `;

      const allParams = [...setParams, ...whereParams];
      query = this.convertToPostgreSQLQuery(query);
      const updateResult = await this.executeQuery(query, allParams);

      // Fetch the updated records to return in data array
      let updatedRecords = [];
      if (updateResult.length > 0) {
        const { whereClause: selectWhereClause, params: selectParams } =
          this.queryBuilder.buildConditions();
        let selectQuery = `SELECT * FROM ${this.settings.table} ${selectWhereClause}`;
        selectQuery = this.convertToPostgreSQLQuery(selectQuery);
        try {
          updatedRecords = await this.executeQuery(selectQuery, selectParams);
        } catch (selectError) {
          console.warn("Could not fetch updated records:", selectError);
          updatedRecords = [data]; // Return the data that was sent for update
        }
      }

      return {
        data: updatedRecords,
        affectedRows: updateResult.length,
      };
    } catch (error) {
      throw new Error(
        `Update operation failed: ${error instanceof Error ? error.message : error}`,
      );
    }
  }

  /**
   * Advanced upsert (INSERT ... ON CONFLICT DO UPDATE)
   */
  async advancedUpsert(): Promise<IQueryResult> {
    try {
      if (!this.settings?.table) {
        throw new Error("Table name is required");
      }

      const data = this.dataFormatter.buildPushData();

      if (_.isEmpty(data)) {
        throw new Error("No data provided for upsert operation");
      }

      // Add unique key data if provided
      if (this.settings.uniqueKey) {
        _.merge(data, this.settings.uniqueKey);
      }

      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = columns.map((_, index) => `$${index + 1}`).join(", ");

      // For PostgreSQL, we need to specify the conflict column(s)
      const conflictColumns = this.settings.uniqueKey
        ? Object.keys(this.settings.uniqueKey)
        : ['id']; // Default to 'id' if no unique key specified

      // Build ON CONFLICT DO UPDATE clause
      const updateClauses = columns
        .filter(
          (col) => !this.settings?.uniqueKey || !this.settings.uniqueKey[col],
        )
        .map((col) => `${col} = EXCLUDED.${col}`);

      const query = `
        INSERT INTO ${this.settings.table} (${columns.join(", ")})
        VALUES (${placeholders})
        ${updateClauses.length ? `ON CONFLICT (${conflictColumns.join(", ")}) DO UPDATE SET ${updateClauses.join(", ")}` : ""}
        RETURNING *
      `;

      const upsertResult = await this.executeQuery(query, values);

      // PostgreSQL with RETURNING * gives us the record directly
      const upsertedRecord = upsertResult.length > 0 ? upsertResult[0] : null;
      const affectedRows = upsertResult.length;

      return {
        data: upsertedRecord ? [upsertedRecord] : [data],
        affectedRows: affectedRows,
        insertId: upsertedRecord?.id || null,
      };
    } catch (error) {
      throw new Error(
        `Upsert operation failed: ${error instanceof Error ? error.message : error}`,
      );
    }
  }

  /**
   * Advanced delete with conditions and limits
   */
  async advancedDelete(): Promise<IQueryResult> {
    try {
      if (!this.settings?.table) {
        throw new Error("Table name is required");
      }

      const { whereClause, params } = this.queryBuilder.buildConditions();

      if (!whereClause) {
        throw new Error("WHERE conditions are required for delete operation");
      }

      const limitClause = this.queryBuilder.buildLimit();

      // First, fetch the records that will be deleted to return their IDs
      let deletedRecords = [];
      let selectQuery = `
        SELECT * FROM ${this.settings.table}
        ${whereClause}
        ${limitClause}
      `;
      selectQuery = this.convertToPostgreSQLQuery(selectQuery);

      try {
        deletedRecords = await this.executeQuery(selectQuery, params);
      } catch (selectError) {
        console.warn("Could not fetch records before deletion:", selectError);
      }

      // Now perform the delete operation
      let deleteQuery = `
        DELETE FROM ${this.settings.table}
        ${whereClause}
        ${limitClause}
      `;
      deleteQuery = this.convertToPostgreSQLQuery(deleteQuery);

      const deleteResult = await this.executeQuery(deleteQuery, params);

      // Extract only IDs from deleted records for the response
      const primaryKeyColumn = await this.databaseUtils.getPrimaryKeyColumn();
      const deletedIds = deletedRecords.map((record) => {
        // Try the detected primary key first, then common ID field names
        if (primaryKeyColumn && record[primaryKeyColumn] !== undefined) {
          return record[primaryKeyColumn];
        }
        return (
          record.id || record.ID || record.pk || record.primary_key || record
        );
      });

      return {
        data: deletedIds.length > 0 ? deletedIds : [],
        affectedRows: deleteResult.length,
      };
    } catch (error) {
      throw new Error(
        `Delete operation failed: ${error instanceof Error ? error.message : error}`,
      );
    }
  }
}
