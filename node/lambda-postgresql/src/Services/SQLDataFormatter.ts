import { ISQLPayload } from "../Interfaces/ISQLHandler";
import * as _ from "lodash";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

/**
 * SQLDataFormatter - <PERSON>les data formatting and building
 */
export default class SQLDataFormatter {
  private settings: ISQLPayload;
  private fieldsMeta: Record<string, any> = {};

  constructor(settings: ISQLPayload) {
    this.settings = settings;

    // Build field metadata from pushMappingMeta if available
    if (this.settings?.pushMappingMeta) {
      _.each(this.settings.pushMappingMeta, (row) => {
        if (row?.Field?.Dynamic?.value) {
          this.fieldsMeta[row?.Field?.Dynamic?.value] = row?.Field?.Dynamic;
        }
      });
    }
  }

  /**
   * Format value based on field type
   */
  formatValue(key: string, val: any, customType?: string): any {
    try {
      let type = customType || _.get(this.fieldsMeta, [key, "type"]);
      if (!type) {
        return val;
      }

      switch (type) {
        case "boolean":
          val = String(val).toLowerCase().trim();
          if (
            val === "yes" ||
            val === "true" ||
            val === "on" ||
            val === "1" ||
            val === "x"
          ) {
            val = true;
          } else {
            val = false;
          }
          break;
        case "double":
        case "decimal":
        case "float":
        case "number":
        case "int":
        case "bigint":
          val = parseFloat(val);
          if (isNaN(val)) {
            val = null;
          }
          break;
        case "date":
          let localVal = dayjs(val);
          if (localVal.isValid()) {
            val = localVal.format("YYYY-MM-DD");
          }
          break;
        case "datetime":
        case "timestamp":
          let localDateTime = dayjs(val);
          if (localDateTime.isValid()) {
            val = localDateTime.format("YYYY-MM-DD HH:mm:ss");
          }
          break;
        case "time":
          let localTime = dayjs(val);
          if (localTime.isValid()) {
            val = localTime.format("HH:mm:ss");
          }
          break;
        default:
          // String types - no conversion needed
          break;
      }
    } catch (err) {
      console.error(`Format Value Error: ${err}`);
    }
    return val;
  }

  /**
   * Build data object for create/update operations
   */
  buildPushData(): Record<string, any> {
    const result: Record<string, any> = {};

    try {
      // Use pushMapping if available (simpler format)
      if (this.settings?.pushMapping) {
        _.each(this.settings.pushMapping, (val, key) => {
          result[key] = this.formatValue(key, val);
        });
      }
      // Otherwise use pushMappingMeta (complex format)
      else if (this.settings?.pushMappingMeta) {
        _.each(this.settings.pushMappingMeta, (mapping) => {
          const fieldName = mapping.Field?.Dynamic?.value;
          const fieldValue = mapping.Value?.Value;

          if (fieldName && fieldValue !== undefined) {
            result[fieldName] = this.formatValue(fieldName, fieldValue);
          }
        });
      }
    } catch (err) {
      console.error(`Build Push Data Error: ${err}`);
    }

    return result;
  }
}
