AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: SAM template for sql-handler function.

Resources:
  SqlHandlerFunction:
    Type: AWS::Serverless::Function # More info about Function Resource: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction
    Properties:
      Timeout: 600
      MemorySize: 512
      FunctionName: lambda-sql
      CodeUri: .
      Handler: src/api/sql-handler.lambdaHandler
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:CommonLayer:60 # the shared layer
      Runtime: nodejs20.x
      Architectures:
        - arm64
      Policies:
        - AWSLambdaBasicExecutionRole
        - AmazonS3FullAccess
      # Events:
      #   SmsSend:
      #     Type: Api # More info about API Event Source: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#api
      #     Properties:
      #       Path: /send-sql
      #       Method: post
    Metadata:
      # Manage esbuild properties
      BuildMethod: esbuild
      BuildProperties:
        External:
          - /opt/nodejs/commons # the layer module must be declared as external
          - /opt/nodejs/Store/Manager # the layer module must be declared as external
        Minify: true
        Target: es2020
        Sourcemap: false
        EntryPoints:
          - src/api/sql-handler.ts
# Outputs:
#   SqlFunctionApi:
#     Description: API Gateway endpoint URL for Prod stage for LambdaSqlAPI
#     Value: !Sub https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/send-sql
