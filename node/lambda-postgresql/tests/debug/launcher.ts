import { APIGatewayProxyEvent } from "aws-lambda";
import { lambda<PERSON>and<PERSON> } from "../../src/api/sql-handler";

const event: unknown = {"body":{"type":"Payload","payload":{"ApplicationID":"postgresql","FlowProjectID":"68ac2d97670c56d57aacbaac","attrs":{"VersionID":"","Settings":{}},"method":"getDatabases","isClearCache":false,"serviceStorageFolder":"integrations/","serviceStorageBucket":"nc-stg-schema"},"fieldName":"databases","credentials":{"token":{"sessionDetails":[{"label":"Host","value":"localhost"},{"label":"Port","value":"5433"},{"label":"User","value":"noca"}]},"host":"localhost","port":"5433","user":"noca","password":"P@ssw0rd","NocaApp":{"url":"https://devnoca.com","event_url":"https://devnoca.com/job/event/"},"MetaStorage":{"AccountID":"68a42f8a3e6a729b0138dfb2","ConnectionID":"68ac318c627cc2aed6aa654c","Bucket":"noca-ivan-test"}},"storage":{"type":"noca","bucket":"devflowjobs","key":"68a42f8a3e6a729b0138dfb2/builder/lookup/files/","staticKey":"68a42f8a3e6a729b0138dfb2/builder/static/","NodeID":"postgresql","serviceStorageFolder":"integrations/","serviceStorageBucket":"nc-stg-schema"}}}
import { Context, Callback } from "aws-lambda";

const context: Context = {
  // Add necessary context properties here
  callbackWaitsForEmptyEventLoop: false,
  functionName: "lambda-salesforce-trigger",
  functionVersion: "$LATEST",
  invokedFunctionArn: "arn:aws:lambda:us-east-1:**********:function:lambda-salesforce-trigger",
  memoryLimitInMB: "128",
  awsRequestId: "unique-request-id",
  logGroupName: "/aws/lambda/lambda-salesforce-trigger",
  logStreamName: "2023/10/10/[$LATEST]abcdef14567890",
  getRemainingTimeInMillis: () => 30000,
  done: (error?: Error, result?: any) => { },
  fail: (error: Error | string) => { },
  succeed: (messageOrObject: any) => { }
};

try {
  const callback: Callback = (error, result) => {
    if (error) {
      console.error(error);
    } else {
      console.log(result);
    }
  };
  const result = lambdaHandler(event as APIGatewayProxyEvent, context, callback) as Promise<any>;
  result.then(result => {
    console.log("Debugging result:", result)

  });
  result.then((result) => {
    console.log("Debugging result:", result);
  });
} catch (err) {
  console.error(err);
}
