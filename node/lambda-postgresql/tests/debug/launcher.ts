import { APIGatewayProxyEvent } from "aws-lambda";
import { lambda<PERSON>and<PERSON> } from "../../src/api/sql-handler";

const event: unknown = {"body":{"type":"Payload","credentials":{"token":{"sessionDetails":[{"label":"Host","value":"nf593332.mysql.tools"},{"label":"User","value":"nf593332_test"}]},"password":"b9+LgZ~75r","host":"nf593332.mysql.tools","user":"nf593332_test","NocaApp":{"url":"https://devnoca.com","event_url":"https://devnoca.com/job/event/"},"MetaStorage":{"AccountID":"68a42f8a3e6a729b0138dfb2","ConnectionID":"68a43ccaf77f2cc1dcc98034","Bucket":"noca-ivan-test"}},"payload":{"database":"nf593332_test","table":"users","action":"create","pushMappingMeta":[{"_id":"3312c76e-f72c-439a-8ca7-4dfa257a1c67","Field":{"_id":"38f80cda-01f0-408c-83ea-7c3f5f025d48","Type":"dynamic","Dynamic":{"value":"name","label":"name","type":"varchar","required":true},"Value":""},"Value":{"_id":"972bed16-c5db-4468-ac63-383a589ae756","Type":"text","Dynamic":{},"Value":"Alan Noca"}},{"_id":"7a2e3211-2e18-4cc6-92f5-36e1c7a49e6e","Field":{"_id":"d8bd405a-a0b8-4f9a-bd07-d987aeee68d6","Type":"dynamic","Dynamic":{"value":"email","label":"email","type":"varchar","required":true},"Value":""},"Value":{"_id":"40f845f3-9ac5-4099-846c-2459d04c2271","Type":"text","Dynamic":{},"Value":"<EMAIL>"}}],"pushMapping":{"name":"Alan Noca","email":"<EMAIL>"}},"FunctionName":"lambda-sql","fields":[],"storage":{"type":"noca","bucket":"devflowjobs","key":"68a42f8a3e6a729b0138dfb2/68a439b7f77f2cc1dcc97f4c/68a5ae8812e519ed0ede594d/files/","staticKey":"68a42f8a3e6a729b0138dfb2/68a439b7f77f2cc1dcc97f4c/static/","NodeID":"e1be4142-1d91-47c8-84eb-5961ca9ec81f","serviceStorageFolder":"integrations/","serviceStorageBucket":"nc-stg-schema"}}}

import { Context, Callback } from "aws-lambda";

const context: Context = {
  // Add necessary context properties here
  callbackWaitsForEmptyEventLoop: false,
  functionName: "lambda-salesforce-trigger",
  functionVersion: "$LATEST",
  invokedFunctionArn: "arn:aws:lambda:us-east-1:1456789012:function:lambda-salesforce-trigger",
  memoryLimitInMB: "128",
  awsRequestId: "unique-request-id",
  logGroupName: "/aws/lambda/lambda-salesforce-trigger",
  logStreamName: "2023/10/10/[$LATEST]abcdef14567890",
  getRemainingTimeInMillis: () => 30000,
  done: (error?: Error, result?: any) => { },
  fail: (error: Error | string) => { },
  succeed: (messageOrObject: any) => { }
};

try {
  const callback: Callback = (error, result) => {
    if (error) {
      console.error(error);
    } else {
      console.log(result);
    }
  };
  const result = lambdaHandler(event as APIGatewayProxyEvent, context, callback) as Promise<any>;
  result.then(result => {
    console.log("Debugging result:", result)

  });
  result.then((result) => {
    console.log("Debugging result:", result);
  });
} catch (err) {
  console.error(err);
}
