{"name": "lambda-sql", "version": "1.0.0", "description": "ftp connector sample for NodeJS", "main": "app.ts", "repository": "https://github.com/awslabs/aws-sam-cli/tree/develop/samcli/local/init/templates/cookiecutter-aws-sam-hello-nodejs", "author": "SAM CLI", "license": "MIT", "scripts": {"start": "sam local start-api -t ./template.yaml -p 5000 -d 5678", "build": "sam build", "deploy": "sam build && sam deploy --no-confirm-changeset", "unit": "jest", "lint": "eslint '*.ts' --quiet --fix", "compile": "tsc -p tsconfig-debug.json", "launch": "ts-node --project tsconfig-debug.json --transpileOnly -r tsconfig-paths/register ./dist/lambda-sql/tests/debug/launcher.js", "debug": "npm run compile & npm run launch", "test": "npm run compile && npm run launch"}, "dependencies": {"esbuild": "0.17.19", "mysql2": "^3.6.5", "dayjs": "^1.11.13", "lodash": "^4.17.21"}, "devDependencies": {"@jest/globals": "^29.7.0", "@smithy/types": "^3.5.0", "@types/aws-lambda": "^8.10.92", "@types/lodash": "^4.17.10", "@types/node": "^20.5.7", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.0.4"}}